[{"id": "a5e5d174fa57c9a8", "type": "tab", "label": "Persistence Messages", "disabled": false, "info": ""}, {"id": "inject-json", "type": "inject", "z": "a5e5d174fa57c9a8", "name": "Inject", "props": [{"p": "payload"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "{\"deviceName\": \"Node1\",\"devEui\": \"09b838e889168f6d\",\"gatewayId\": \"24e124fffef78542\",\"time\": \"2024-09-19T15:16:44.845+00:00\",\"data\": [{\"conductivity\": 0,\"ph\": 3,\"stateSensor\": \"available\",\"humidity\": 0,\"idSensor\": 2,\"temperature\": 21.3,\"typeSensor\": 2},{\"idSensor\": 5,\"typeSensor\": 5,\"stateSensor\": \"available\",\"temperature\": 21.5,\"humidity\": 71.1}]}", "payloadType": "json", "x": 90, "y": 320, "wires": [["guardar-payload-inyectado"]]}, {"id": "guardar-payload-inyectado", "type": "change", "z": "a5e5d174fa57c9a8", "name": "Save Payload", "rules": [{"t": "set", "p": "injectedPayload", "pt": "flow", "to": "payload", "tot": "msg"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 240, "y": 280, "wires": [["leer-archivo-mensajes"]]}, {"id": "leer-archivo-mensajes", "type": "file in", "z": "a5e5d174fa57c9a8", "name": "Read JSON", "filename": "/data/mensajes_almacenados.json", "filenameType": "str", "format": "utf8", "chunk": false, "sendError": true, "allProps": false, "x": 410, "y": 280, "wires": [["parsear-contenido-leido"]]}, {"id": "parsear-contenido-leido", "type": "json", "z": "a5e5d174fa57c9a8", "name": "Parser JSON", "property": "payload", "action": "obj", "pretty": false, "x": 570, "y": 280, "wires": [["agregar-inyectado-a-leido"]]}, {"id": "agregar-inyectado-a-leido", "type": "function", "z": "a5e5d174fa57c9a8", "name": "Add Payload", "func": "let mensajesAlmacenados = msg.payload || { messages: [] };\nlet injectedPayload = flow.get('injectedPayload'); // Obtener el payload inyectado\n\n// Asegurarse que hay un array de mensajes\nif (!mensajesAlmacenados.messages || !Array.isArray(mensajesAlmacenados.messages)) {\n    mensajesAlmacenados.messages = [];\n}\n\n// Agregar el nuevo mensaje al array\nmensajesAlmacenados.messages.push(injectedPayload);\n\nmsg.payload = JSON.stringify(mensajesAlmacenados); // Actualizar el archivo completo\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 730, "y": 280, "wires": [["guardar-archivo"]]}, {"id": "guardar-archivo", "type": "file", "z": "a5e5d174fa57c9a8", "name": "Save File", "filename": "/data/mensajes_almacenados.json", "filenameType": "str", "appendNewline": false, "createDir": true, "overwriteFile": "true", "encoding": "utf8", "x": 880, "y": 280, "wires": [["debug-archivo-guardado"]]}, {"id": "debug-archivo-guardado", "type": "debug", "z": "a5e5d174fa57c9a8", "name": "Print Message", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1040, "y": 280, "wires": []}, {"id": "revision-conexion", "type": "inject", "z": "a5e5d174fa57c9a8", "name": "Check State", "props": [], "repeat": "10", "crontab": "", "once": true, "onceDelay": 0.1, "topic": "", "x": 120, "y": 420, "wires": [["vaciar-cola"]]}, {"id": "vaciar-cola", "type": "function", "z": "a5e5d174fa57c9a8", "name": "Empty Queue", "func": "var internetAvailable = flow.get('internetAvailable') || false;\n\nif (internetAvailable) {\n    // Leer archivo y vaciar si hay conexión\n    return {payload: 'leer'};\n} else {\n    return null;  // No hace nada si no hay conexión\n}", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 300, "y": 420, "wires": [["leer-archivo-cola"]]}, {"id": "leer-archivo-cola", "type": "file in", "z": "a5e5d174fa57c9a8", "name": "Read Queue", "filename": "/data/mensajes_almacenados.json", "filenameType": "str", "format": "utf8", "chunk": false, "sendError": true, "encoding": "utf8", "allProps": false, "x": 470, "y": 420, "wires": [["parsear-archivo-cola"]]}, {"id": "parsear-archivo-cola", "type": "json", "z": "a5e5d174fa57c9a8", "name": "<PERSON><PERSON><PERSON>", "property": "payload", "action": "obj", "pretty": false, "x": 640, "y": 420, "wires": [["enviar-mensaje-unico"]]}, {"id": "enviar-mensaje-unico", "type": "function", "z": "a5e5d174fa57c9a8", "name": "Send Queue", "func": "let cola = msg.payload;\n\nif (cola.messages && cola.messages.length > 0) {\n    let messageToSend = cola.messages[0];  // Enviar el primer mensaje en la cola\n    node.send({ payload: messageToSend });\n\n    // Eliminar el primer mensaje del array y devolver la cola actualizada\n    cola.messages.shift();\n    return [null, { payload: JSON.stringify(cola) }];\n} else {\n    return [null, null];  // No hacer nada si la cola está vacía\n}", "outputs": 2, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 810, "y": 420, "wires": [["debug-enviado", "0f8635ab72df9b1c"], ["actualizar-archivo-cola"]]}, {"id": "actualizar-archivo-cola", "type": "file", "z": "a5e5d174fa57c9a8", "name": "Update Queue", "filename": "/data/mensajes_almacenados.json", "filenameType": "str", "appendNewline": false, "createDir": true, "overwriteFile": "true", "encoding": "utf8", "x": 980, "y": 440, "wires": [[]]}, {"id": "debug-enviado", "type": "debug", "z": "a5e5d174fa57c9a8", "name": "Print Broker", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 990, "y": 340, "wires": []}, {"id": "1a2b3c4d5e", "type": "inject", "z": "a5e5d174fa57c9a8", "name": "Read JSON", "props": [], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "x": 110, "y": 20, "wires": [["6e0a1b2c3d"]]}, {"id": "6e0a1b2c3d", "type": "file in", "z": "a5e5d174fa57c9a8", "name": "Read File JSON", "filename": "/data/mensajes_almacenados.json", "filenameType": "str", "format": "utf8", "chunk": false, "sendError": false, "encoding": "none", "allProps": false, "x": 280, "y": 20, "wires": [["4d5e6f7g8h"]]}, {"id": "4d5e6f7g8h", "type": "debug", "z": "a5e5d174fa57c9a8", "name": "Print JSON", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 750, "y": 20, "wires": []}, {"id": "vaciar-archivo-inyeccion", "type": "inject", "z": "a5e5d174fa57c9a8", "name": "Erase JSON", "props": [], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "x": 110, "y": 60, "wires": [["leer-archivo-vaciar"]]}, {"id": "leer-archivo-vaciar", "type": "file in", "z": "a5e5d174fa57c9a8", "name": "Read File", "filename": "/data/mensajes_almacenados.json", "filenameType": "str", "format": "utf8", "sendError": true, "allProps": false, "x": 260, "y": 60, "wires": [["vaciar-archivo"]]}, {"id": "vaciar-archivo", "type": "function", "z": "a5e5d174fa57c9a8", "name": "Erase Function", "func": "msg.payload = '{\"messages\":[]}';\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 420, "y": 60, "wires": [["actualizar-archivo-vacio"]]}, {"id": "actualizar-archivo-vacio", "type": "file", "z": "a5e5d174fa57c9a8", "name": "Overwrite", "filename": "/data/mensajes_almacenados.json", "filenameType": "str", "appendNewline": false, "createDir": false, "overwriteFile": "true", "encoding": "utf8", "x": 580, "y": 60, "wires": [["4d5e6f7g8h"]]}, {"id": "186ae9ae8971084b", "type": "mqtt in", "z": "a5e5d174fa57c9a8", "name": "UplinkMessages", "topic": "application/+/device/+/event/up", "qos": "0", "datatype": "auto-detect", "broker": "7cc4ec02c6ef709e", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 100, "y": 220, "wires": [["6c4e2e9711fea272"]]}, {"id": "f1c5708219eb6f00", "type": "function", "z": "a5e5d174fa57c9a8", "name": "Bridge", "func": "// Acceder al objeto `object` dentro del mensaje\nvar decodedData = msg.payload.object;\n\n// Crear un nuevo mensaje que contenga el deviceName y todo el objeto `object`\nvar output = {\n    deviceName: msg.payload.deviceInfo.deviceName,\n    devEui: msg.payload.deviceInfo.devEui,\n    gatewayId: msg.payload.rxInfo[0].gatewayId,\n    time: msg.payload.time,\n    ...decodedData // Usar el operador spread para incluir todas las propiedades de `object`\n};\n\n// Asignar el nuevo objeto al payload del mensaje\nmsg.payload = output;\n\n// Retornar el mensaje modificado\nreturn msg;\n", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 370, "y": 220, "wires": [["guardar-payload-inyectado"]]}, {"id": "0f8635ab72df9b1c", "type": "mqtt out", "z": "a5e5d174fa57c9a8", "name": "lorawanUplink", "topic": "lorawanUplink", "qos": "0", "retain": "", "respTopic": "", "contentType": "application/json", "userProps": "", "correl": "", "expiry": "", "broker": "eedd33b6dd62c0de", "x": 1000, "y": 380, "wires": []}, {"id": "6c4e2e9711fea272", "type": "switch", "z": "a5e5d174fa57c9a8", "name": "Filter", "property": "payload.fPort", "propertyType": "msg", "rules": [{"t": "neq", "v": "0", "vt": "num"}], "checkall": "true", "repair": false, "outputs": 1, "x": 250, "y": 220, "wires": [["f1c5708219eb6f00"]]}, {"id": "change-state", "type": "inject", "z": "a5e5d174fa57c9a8", "name": "Change State", "props": [], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "true", "payloadType": "bool", "x": 110, "y": 120, "wires": [["connection-state"]]}, {"id": "connection-state", "type": "function", "z": "a5e5d174fa57c9a8", "name": "Connection State", "func": "var previousState = flow.get('internetAvailable') || false;\nvar newState = !previousState;\nflow.set('internetAvailable', newState);\n\nif (previousState !== newState) {\n    return { payload: newState };\n} else {\n    return null;  // No enviar nada si no hay cambio\n}", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 290, "y": 120, "wires": [["print-state"]]}, {"id": "print-state", "type": "debug", "z": "a5e5d174fa57c9a8", "name": "Print State", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "x": 670, "y": 120, "wires": []}, {"id": "server-ping", "type": "inject", "z": "a5e5d174fa57c9a8", "name": "Server Ping", "props": [], "repeat": "10", "crontab": "", "once": true, "onceDelay": 0.1, "topic": "", "x": 110, "y": 160, "wires": [["ping-node"]]}, {"id": "ping-node", "type": "ping", "z": "a5e5d174fa57c9a8", "mode": "triggered", "name": "<PERSON>", "host": "*************", "timer": "", "inputs": 1, "x": 250, "y": 160, "wires": [["update-connection-state"]]}, {"id": "update-connection-state", "type": "function", "z": "a5e5d174fa57c9a8", "name": "Update Connection State", "func": "var previousState = flow.get('internetAvailable') || false;\nvar newState = msg.payload !== false;  // Si el Ping tiene éxito, la conexión está disponible\n\nflow.set('internetAvailable', newState);\n\nif (previousState !== newState) {\n    return { payload: newState };  // Enviar si hay cambio\n} else {\n    return null;  // No enviar nada si no hay cambio\n}", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 450, "y": 160, "wires": [["print-state"]]}, {"id": "fc30501cd613b88e", "type": "mqtt out", "z": "a5e5d174fa57c9a8", "name": "DownlinkMessages", "topic": "", "qos": "0", "retain": "", "respTopic": "", "contentType": "", "userProps": "", "correl": "", "expiry": "", "broker": "a0d4bb29d6008f4e", "x": 630, "y": 520, "wires": []}, {"id": "aa2bd18fb02f4167", "type": "function", "z": "a5e5d174fa57c9a8", "name": "Command", "func": "msg.payload = {\n  \"deviceName\": \"NodeRFM95\",\n  \"devEui\": \"0560a4861eb13afe\",\n  \"command\": \"offset\",\n  \"data\": [\n    {\n      \"conductivity\": 0,\n      \"humidity\": 0,\n      \"idSensor\": 1,\n      \"temperature\": 0,\n      \"typeSensor\": 1\n    }\n  ]\n}\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 250, "y": 520, "wires": [["51797b1f250d8fd4"]]}, {"id": "84330b8f447bb3ef", "type": "inject", "z": "a5e5d174fa57c9a8", "name": "", "props": [{"p": "payload"}, {"p": "topic", "vt": "str"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "date", "x": 100, "y": 520, "wires": [["aa2bd18fb02f4167"]]}, {"id": "af3fcebaaf94302e", "type": "debug", "z": "a5e5d174fa57c9a8", "name": "Print Commands", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 630, "y": 480, "wires": []}, {"id": "51797b1f250d8fd4", "type": "function", "z": "a5e5d174fa57c9a8", "name": "encodeDownlink", "func": "let devEui = msg.payload.devEui;\n\nmsg.topic = `application/d29062e2-1d00-4025-850a-1f13cd7349c7/device/${devEui}/command/down`;\n\nvar codedData = msg.payload.data;\n\nvar output = {\n    devEui: msg.payload.devEui,\n    confirmed: false,                      \n    fPort: 1,\n    object: {\n        command: msg.payload.command,\n        data: codedData\n        } \n};\n\nmsg.payload = output;\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 420, "y": 520, "wires": [["af3fcebaaf94302e", "fc30501cd613b88e"]]}, {"id": "26af71893e2631ee", "type": "mqtt in", "z": "a5e5d174fa57c9a8", "name": "lorawanDownlink", "topic": "lorawanDownlink", "qos": "0", "datatype": "auto-detect", "broker": "c32c82b1b264670b", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 100, "y": 580, "wires": [["51797b1f250d8fd4"]]}, {"id": "7cc4ec02c6ef709e", "type": "mqtt-broker", "name": "cs-mqtt", "broker": "localhost", "port": "1883", "clientid": "", "autoConnect": true, "usetls": false, "protocolVersion": "4", "keepalive": "60", "cleansession": true, "birthTopic": "", "birthQos": "0", "birthRetain": "false", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closeQos": "0", "closeRetain": "false", "closePayload": "", "closeMsg": {}, "willTopic": "", "willQos": "0", "willRetain": "false", "willPayload": "", "willMsg": {}, "userProps": "", "sessionExpiry": ""}, {"id": "eedd33b6dd62c0de", "type": "mqtt-broker", "name": "", "broker": "*************", "port": "1883", "clientid": "sending-device-dev", "autoConnect": true, "usetls": false, "protocolVersion": "5", "keepalive": "60", "cleansession": true, "birthTopic": "", "birthQos": "0", "birthRetain": "false", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closeQos": "0", "closeRetain": "false", "closePayload": "", "closeMsg": {}, "willTopic": "", "willQos": "0", "willRetain": "false", "willPayload": "", "willMsg": {}, "userProps": "", "sessionExpiry": ""}, {"id": "a0d4bb29d6008f4e", "type": "mqtt-broker", "name": "", "broker": "localhost", "port": "1883", "clientid": "", "autoConnect": true, "usetls": false, "protocolVersion": "4", "keepalive": "60", "cleansession": true, "birthTopic": "", "birthQos": "0", "birthRetain": "false", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closeQos": "0", "closeRetain": "false", "closePayload": "", "closeMsg": {}, "willTopic": "", "willQos": "0", "willRetain": "false", "willPayload": "", "willMsg": {}, "userProps": "", "sessionExpiry": ""}, {"id": "c32c82b1b264670b", "type": "mqtt-broker", "name": "", "broker": "*************", "port": "1883", "clientid": "receiving-device", "autoConnect": true, "usetls": false, "protocolVersion": "5", "keepalive": "60", "cleansession": true, "birthTopic": "", "birthQos": "0", "birthRetain": "false", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closeQos": "0", "closeRetain": "false", "closePayload": "", "closeMsg": {}, "willTopic": "", "willQos": "0", "willRetain": "false", "willPayload": "", "willMsg": {}, "userProps": "", "sessionExpiry": ""}]