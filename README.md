# Gapy Node

This project is a monitoring system based on the ESP32, designed to obtain data from sensors through the RS485 bus and transmit it via LoRa. The system integrates environmental and soil temperature and humidity sensors, soil nutrient sensors, as well as air quality sensors.

The system is configured to transmit data for periods of time and enter a low power mode. The data collected from the sensors is stored in CSV files on an SD card for further analysis.

#### RGB Handling Functions

The project defines the functionalities necessary to control an RGB LED connected to an ESP32-C3-MINI-DK board. It provides an interface to initialize the RGB LED and to change its color based on predefined color enumerations.

| Function        | Description                                                  |
|-----------------|--------------------------------------------------------------|
| `initializeRGB()` | Initializes the RGB LED by setting up the GPIO pins as outputs and initializing the LED to off (BLACK). This function uses the `Adafruit_NeoPixel` library to begin the LED strip and set its initial state. |
| `setColor(Color color)` | Changes the color of the RGB LED to the specified color. The function accepts an enumeration that represents various colors such as BLACK, RED, GREEN, BLUE, MAGENTA, YELLOW, CYAN, and WHITE. This is achieved by setting the pixel color using the `pixels.setPixelColor` method and updating the LED with `pixels.show()`. |
| `cycleColors()` | Iterates through all the colors defined in the `Color` enumeration, changing the RGB LED color every second. This function is useful for verifying that the LED can display the full range of colors and for providing a visual indication of the system's functionality. |

##### Enum Definition

The `Color` enum defines the colors that the RGB LED can display:

```cpp
enum Color
{
    BLACK,
    RED,
    GREEN,
    BLUE,
    MAGENTA,
    YELLOW,
    CYAN,
    WHITE
};
```

##### Example Usage
Below is an example of how to use the initializeRGB, setColor, and cycleColors functions in your main program:

```cpp
#include "RGBFunctions.hpp"

void setup() {
  Serial.begin(115200);
  initializeRGB();
}

void loop() {
  cycleColors();
}
```
In this example, the setup function initializes the RGB LED, and the loop function continuously calls cycleColors to cycle through the colors.

##### Purpose
The RGB handling functions serve several purposes:

1. Initialization: initializeRGB sets up the RGB LED for use, ensuring it is ready to display colors.
2. Color Control: setColor allows the LED to display any color from the predefined Color enum, providing flexibility in visual output.
3. Verification: cycleColors can be used to verify that each color channel of the RGB LED is functioning correctly and to provide a visual indication of system status.

These functions collectively ensure that the RGB LED can be easily controlled and tested, making it a useful component for various projects involving visual feedback.

#### Button Handling Functions

This section describes the functionality for handling button interactions, allowing the device to trigger specific actions based on how long the button is pressed. The button logic supports multiple time-sensitive actions, offering a dynamic way to interact with the device.


| Function             | Description                                                   |
|----------------------|---------------------------------------------------------------|
| `initializeButton()` | Initializes the button pin as an input with a pull-up resistor, preparing it for interaction. |
| `printColorState(ButtonColorState colorState)` | Prints the color state to the Serial monitor as the button is pressed, providing real-time feedback on the interaction. |
| `checkButtonPress()` | Monitors and debounces the button press, changing the RGB LED color according to the press duration. After releasing the button, it triggers specific actions based on the last color state: WHITE for a short press, MAGENTA for a 2-second press, YELLOW for a 5-second press, and CYAN for an 8-second press. |

This modular approach to button handling allows for easy expansion or modification of the button's behavior and associated actions within the device's functionality.

#### RS485 Handling Functions

| Function              | Description                                                                                  |
|-----------------------|----------------------------------------------------------------------------------------------|
| `initializeRS485()`   | Initializes the RS485 module with the defined baud rate.                                      |
| `calculateCRC()`      | Calculates the CRC16 Modbus for the provided data.                                            |
| `sendSlaveIDRequest()`| Sends a request to a slave device to read its ID.                                             |
| `setSlaveID()`        | Sets a new ID for a slave device.                                                             |

#### Link Sensor Handling Functions

| Function                     | Description                                                                                 |
|------------------------------|---------------------------------------------------------------------------------------------|
| `createScanRegisterFile()`   | Creates the scan register file with an empty JSON object.                                    |
| `updateScanRegisterFile()`   | Updates the scan register file with a new sensor ID and its parameters.                      |
| `deleteScanRegisterFile()`   | Deletes the scan register file.                                                              |
| `createParameterFile()`      | Creates a parameter file with an empty JSON object.                                          |
| `updateParameterFile()`      | Updates the parameter file with provided JSON data.                                          |
| `deleteParameterFile()`      | Deletes a specified parameter file.                                                          |
| `generateRegistersSensor()`  | Generates parameter files for a sensor based on the sensor database.                         |
| `getTotalSensors()`          | Returns the total number of sensors from the sensor database file.                           |
| `readRegisteredIDs()`        | Reads and returns a vector of registered sensor IDs from the scan register file.             |
| `generateNewID()`            | Generates a new sensor ID by finding the next available ID.                                  |
| `deleteParameterFiles()`     | Deletes all parameter files for a specified sensor ID.                                       |
| `clearScanRegisterFile()`    | Clears the scan register file, removing all registered sensor IDs.                           |
| `resetAllSensors()`          | Resets all sensors by deleting their parameter files and clearing the scan register file.    |
| `scanAndRegisterSensors()`   | Scans for sensors, registers new sensors, updates existing sensor IDs, and generates parameter files. |

#### Routine Sensor Handling Functions

| Function                      | Description                                                                                 |
|-------------------------------|---------------------------------------------------------------------------------------------|
| `sendSlaveIDRequestRoutine()` | Sends a request to a slave device to read its ID and checks for a valid response.           |
| `readSensorData()`            | Reads the sensor data from the specified parameter file and updates the value with a factor.|
| `updateParameterValue()`      | Updates the parameter value in the specified JSON file.                                     |
| `sensorRoutineCallback()`     | Callback function for the sensor routine, reads and updates sensor data periodically.       |


#### Sleep Mode Handling Functions

| Function                   | Description                                                                                 |
|----------------------------|---------------------------------------------------------------------------------------------|
| `goToSleepModeDevice()`    | Puts the device into deep sleep mode, configured to wake up on a GPIO interrupt or timer.   |
| `timerBeforeSleep()`       | Checks if the predefined active time has elapsed and sets the flag to go to sleep.          |
| `checkGoToSleepMode()`     | Verifies if the device should go to sleep based on the active time and sleep mode flag.     |
| `manageSleepMode()`        | General function to manage the sleep mode by calling `checkGoToSleepMode()`.                |
| `setSleepMode(bool enable)`| Enables or disables the sleep mode based on the parameter. YELLOW color configures the button configuration and attaches the interrupt for the button press                                  |

#### LoRaWAN Handling Functions

| Function                        | Description                                                                                      |
|----------------------------------|--------------------------------------------------------------------------------------------------|
| `sendATCommand()`  | Sends an AT command to the LoRaWAN module and waits for a response within the specified timeout. |
| `checkATResponse()` | Checks if the response from the LoRaWAN module contains the expected string.                   |
| `executeATCommand()`             | Sends the `AT` command to the LoRaWAN module multiple times and checks for a valid response.     |
| `initalizeLoRaWAN()`             | Initializes the LoRaWAN module, sets up communication, and attempts to join the network if needed. |

#### Join Handling Functions

| Function                        | Description                                                                                      |
|----------------------------------|--------------------------------------------------------------------------------------------------|
| `checkNetworkConnection()`       | Sends a test uplink command (`AT+SEND=2:0:ABCD`) to verify if the device is connected to the network. |
| `joinNetworkIfNeeded()`          | Checks if the device is connected to the network, and if not, attempts to join using OTAA with the `AT+CERTIF=1` and `AT+JOIN=1` commands. |

#### Downlink Handling Functions

| Function                              | Description                                                                                                           |
|---------------------------------------|-----------------------------------------------------------------------------------------------------------------------|
| `writeOffsetSensor()`  | Writes the offset value to the sensor over RS485 communication and stores the data in the corresponding parameter file. |
| `processLoRaWANResponse()`           | Processes the response from the LoRaWAN module and extracts the downlink payload if available.                          |
| `convertStringToByteArray()`  | Converts the received payload from a string to an array of bytes.                                                       |
| `storeOffsetInParameter()`  | Stores the decoded offset in the sensor's corresponding JSON parameter file.                                            |
| `decodeOffset()`  | Decodes the received offset value using the sensor's factor and sign and returns the decoded float value.               |
| `findSensorById()`  | Finds and returns the sensor information by its ID from the scan register JSON file.                                    |
| `readScanRegisterLoRaWAN()`                        | Reads and deserializes the scan register JSON file to retrieve sensor information.                                       |
| `processOffsetsFromCommand()`                      | Processes the received offset command, updating sensor parameters and applying offsets if applicable.                   |
| `processCommandLoRaWAN()`                          | Processes the received command based on the command ID, such as offset commands or other specific commands.             |
| `downlinkProcessingLoRaWAN()`                      | Handles the downlink payload, converts it to a byte array, and processes the command embedded in the downlink payload.  |

#### Uplink Handling Functions

| Function                                | Description                                                                                                           |
|-----------------------------------------|-----------------------------------------------------------------------------------------------------------------------|
| `readScanRegister()`                    | Reads the scan register JSON file from SPIFFS and returns the document containing sensor information.                  |
| `checkSensorState()`          | Sends an RS485 request to check the state of a sensor by its ID and validates the response using a CRC check.          |
| `readSensorData()`| Reads and deserializes the sensor data from the specified JSON file in SPIFFS.                                         |
| `buildPayload()`                        | Builds the uplink payload by reading sensor information, calculating scaled data, and formatting it for LoRaWAN uplink.|
| `uplinkMessageLoRaWAN()`                | Constructs and sends the uplink message to the LoRaWAN module and handles the response.                                |
| `delayUplinkMessageLoRaWAN()`           | Controls the timing of the uplink message, ensuring it is sent at defined intervals, and enables the uplink task.      |

#### Credentials Handling Functions

| Function                                | Description                                                                                                           |
|-----------------------------------------|-----------------------------------------------------------------------------------------------------------------------|
| `loadLoRaWANCredentials()`              | Loads the LoRaWAN credentials (`APPEUI`, `DEVEUI`, `APPKEY`) from the JSON file stored in SPIFFS.                      |
| `updateDevEUIinJson()`                  | Updates the `DEVEUI` in the LoRaWAN credentials JSON file.                                                            |
| `generateAndSaveDevEUI()`               | Generates a `DEVEUI` based on the ESP32 MAC address and saves it in the credentials JSON file.                         |
| `sendLoRaWANCommand()`    | Sends an AT command to the LoRaWAN module and verifies the response.                                                   |
| `sendLoRaWANResetCommand()`| Sends a reset AT command (`ATZ`) to the LoRaWAN module and checks the response.                                         |
| `sendResetLoRaWAN()`                    | Resets the LoRaWAN module by sending the `ATZ` command.                                                               |
| `sendAppEUIToLoRaWAN()`                 | Sends the `APPEUI` from the credentials structure to the LoRaWAN module using the `AT+APPEUI` command.                 |
| `sendAppKeyToLoRaWAN()`                 | Sends the `APPKEY` from the credentials structure to the LoRaWAN module using the `AT+APPKEY` command.                 |
| `sendDevEUIToLoRaWAN()`                 | Sends the `DEVEUI` from the credentials structure to the LoRaWAN module using the `AT+DEUI` command.                   |
| `setupCredentialsLoRaWAN()`             | Initializes the LoRaWAN module, resets it, generates the `DEVEUI`, loads credentials, and sends the credentials to the module. |
