function encodeDownlink(input) {
  // JSON de apoyo con la información de sensores (sensorInfo)
  var sensorInfo = {
    1: {
      name: "Soil Sensor THC-S",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "conductivity", factor: 1.0 },
      ],
    },
    2: {
      name: "Soil Sensor THCPH-S",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "conductivity", factor: 1.0 },
        { id: 4, name: "ph", factor: 10.0 },
      ],
    },
    3: {
      name: "Soil Sensor NPK-S",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "conductivity", factor: 1.0 },
        { id: 4, name: "ph", factor: 10.0 },
        { id: 5, name: "nitrogen", factor: 1.0 },
        { id: 6, name: "phosphorus", factor: 1.0 },
        { id: 7, name: "potassium", factor: 1.0 },
      ],
    },
    4: {
      name: "Env Sensor SAQ-TH",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
      ],
    },
    5: {
      name: "Env Sensor BY-TH",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
      ],
    },
    6: {
      name: "Env Sensor PS",
      parameters: [{ id: 1, name: "photosynthetic", factor: 1.0 }],
    },
    7: {
      name: "Env Sensor SWC-C",
      parameters: [{ id: 1, name: "windSpeed", factor: 10.0 }],
    },
    8: {
      name: "Env Sensor RG",
      parameters: [{ id: 1, name: "rainGauge", factor: 10.0 }],
    },
    9: {
      name: "Env Sensor BY-THCO2",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "co2", factor: 1.0 },
      ],
    },
    10: {
      name: "Env Sensor BY-THISCO2",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "co2", factor: 1.0 },
      ],
    },
    11: {
      name: "Env Sensor SAQ-CO2",
      parameters: [{ id: 1, name: "co2", factor: 1.0 }],
    },
    12: {
      name: "Env Sensor UWD",
      parameters: [
        { id: 1, name: "windSpeed", factor: 100.0 },
        { id: 2, name: "windDirection", factor: 1.0 },
        { id: 3, name: "temperature", factor: 10.0 },
        { id: 4, name: "humidity", factor: 10.0 },
        { id: 5, name: "pm2.5", factor: 1.0 },
        { id: 6, name: "pm10", factor: 1.0 },
        { id: 7, name: "noise", factor: 10.0 },
        { id: 8, name: "pressure", factor: 10.0 },
        { id: 9, name: "rainfall", factor: 10.0 },
      ],
    },
    13: {
      name: "Water Quality 5 in 1",
      parameters: [
        { id: 1, name: "temperature", factor: 100.0 },
        { id: 2, name: "ph", factor: 100.0 },
        { id: 3, name: "salinity", factor: 1000.0 },
        { id: 4, name: "conductivity", factor: 0.1 },
      ],
    },
    14: {
      name: "Water Level Pressure",
      parameters: [
        { id: 1, name: "highLevel", factor: 1.0 },
        { id: 2, name: "lowLevel", factor: 1.0 },
      ],
    },
    15: {
      name: "Water Level Ultrasonic",
      parameters: [{ id: 1, name: "distance", factor: 1.0 }],
    },
  };

  // Inicia la cadena de payload con el valor correspondiente al comando
  let commandValue = input.data.command === "offset" ? "01" : "00"; // Si es "offset", coloca 01; de lo contrario, 00
  let hexPayload = commandValue; // Inicializa el hexPayload con el valor del comando

  // Usar un bucle 'for' para recorrer el array de sensores
  for (let i = 0; i < input.data.data.length; i++) {
    let sensor = input.data.data[i];
    let sensorHex = "";
    let idSensor = sensor.idSensor.toString(16).padStart(2, "0");
    sensorHex += `${idSensor}`; // Añadir el idSensor en formato hexadecimal

    // Obtener los parámetros del sensor
    let parameters = sensorInfo[sensor.idSensor].parameters;

    // Usar un bucle 'for' para recorrer los parámetros del sensor
    for (let j = 0; j < parameters.length; j++) {
      let param = parameters[j];
      if (sensor[param.name] !== undefined) {
        // Convertir el valor a hexadecimal con manejo de signo
        let value = Math.abs(Math.round(sensor[param.name] * param.factor))
          .toString(16)
          .padStart(4, "0");
        let sign = sensor[param.name] >= 0 ? "00" : "01"; // 00 para positivo, 01 para negativo
        sensorHex += `${sign}${value}`; // Añadir el valor codificado con signo, LSB y MSB
      }
    }

    hexPayload += sensorHex;
  }

  // Convertir la cadena de hexadecimal en un array de bytes
  let bytes = hexPayload.match(/.{1,2}/g).map((byte) => parseInt(byte, 16));

  return { bytes: bytes };
}

// Ejemplo de uso con un JSON que tenga una clave `object` y dentro `data`
let jsonData = {
  data: {
    command: "offset",
    data: [
      {
        idSensor: 2,
        temperature: 21.3,
        humidity: 5,
        conductivity: 100,
        ph: 2,
      },
      {
        idSensor: 5,
        temperature: -1.5,
        humidity: -10.2,
      },
    ],
  },
};

// Llamada a la función encodeDownlink
let result = encodeDownlink(jsonData);
console.log("Bytes Result:", result.bytes);
