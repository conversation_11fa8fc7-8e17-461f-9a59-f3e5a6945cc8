// src\AllNode\LoRaWAN\LoRaWANFunctions.hpp
#ifndef LORAWANFUNCTIONS_HPP
#define LORAWANFUNCTIONS_HPP

String sendATCommand(String command, int timeout)
{
    String response = "";
    lorawanSerial.println(command);
    Serial.print("Enviando comando: ");
    Serial.println(command);

    unsigned long startTime = millis();
    while (millis() - startTime < timeout)
    {
        if (lorawanSerial.available())
        {
            char c = lorawanSerial.read();
            response += c;
        }
    }

    Serial.print("Respuesta recibida: ");
    Serial.println(response);

    return response;
}

bool checkATResponse(String response, String expected)
{
    if (response.indexOf(expected) != -1)
    {
        Serial.println("Valid response received.");
        return true;
    }
    else
    {
        Serial.println("Invalid response or error.");
        return false;
    }
}

void executeATCommand()
{
    for (int i = 0; i < 2; i++)
    {
        String response = sendATCommand("AT", 500);
        if (checkATResponse(response, "OK"))
        {
            Serial.println("AT command executed successfully.");
        }
        else
        {
            Serial.println("Error executing AT command.");
        }
    }
}

#include "JoinFunctions.hpp"
#include "DownlinkFunctions.hpp"
#include "UplinkFunctions.hpp"
#include "CredentialsFunctions.hpp"

void initalizeLoRaWAN()
{
    lorawanSerial.begin(BAUD_LORAWAN, SERIAL_8N1, RX_LORAWAN_PIN, TX_LORAWAN_PIN);

    Serial.println("Initializing...");
    executeATCommand();

    joinNetworkIfNeeded();
}

#endif // LORAWANFUNCTIONS_HPP