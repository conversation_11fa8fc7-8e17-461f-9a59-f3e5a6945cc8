// src\AllNode\LoRaWAN\JoinFunctions.hpp
#ifndef JOINFUNCTIONS_HPP
#define JOINFUNCTIONS_HPP

bool checkNetworkConnection()
{
    String command = "AT+SEND=2:0:ABCD";
    String response = sendATCommand(command.c_str(), TIME_CHECK_NETCONN);

    if (checkATResponse(response, "OK"))
    {
        return true;
    }
    else
    {
        return false;
    }
}

void joinNetworkIfNeeded()
{
    connectedToNetwork = checkNetworkConnection();
    if (!connectedToNetwork)
    {
        String response = sendATCommand("AT+CERTIF=1", TIME_CERTIF);
        if (checkATResponse(response, "+EVT:JOINED"))
        {
            Serial.println("Successfully joined the network.");
        }
        Serial.println("Not connected, attempting join...");
        response = sendATCommand("AT+JOIN=1", TIME_JOIN);
        if (checkATResponse(response, "+EVT:JOINED"))
        {
            connectedToNetwork = true;
            Serial.println("Joined network successfully.");
        }
        else
        {
            Serial.println("Failed to join network.");
        }
    }
    else
    {
        Serial.println("Already connected to the network.");
    }
}

#endif // JOINFUNCTIONS_HPP