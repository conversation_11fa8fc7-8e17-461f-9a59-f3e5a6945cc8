# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](http://semver.org/).

## [0.1.0] - 2024-10-17
### Added
- RGB handling functions for controlling an RGB LED:
  - `initializeRGB()` to initialize the RGB LED.
  - `setColor(Color color)` to change the color of the RGB LED.
  - `cycleColors()` to iterate through all the colors in the `Color` enumeration, changing the LED color every second.

## [0.2.0] - 2024-10-17

### Added
- `RGBFunctions.hpp` with functionalities to control an RGB LED, enabling the device to display different colors (WHITE, MAGENTA, YELLOW, CYAN) based on specific conditions or actions.
- `ButtonFunctions.hpp` introduces advanced button handling that allows the device to trigger different actions based on the duration of the button press. This includes setting the RGB LED to different colors depending on how long the button is pressed and executing specific actions when the button is released.

### Changed
- Enhanced button debounce logic in `ButtonFunctions.hpp` to improve the accuracy of press duration detection and prevent false triggers.

### Fixed
- Adjustments to RGB LED color display timing to synchronize with button press durations more accurately.

This update significantly enhances the device's interactive capabilities, offering users a more intuitive and responsive way to control its network connectivity and visual feedback through the RGB LED. The introduction of time-sensitive button actions opens up new possibilities for user interaction and device functionality.

## [0.0.3] - 2024-10-17

### Added
- `RS485Functions.hpp` with functionalities to handle RS485 communication, including initialization, CRC calculation, sending requests, and setting slave IDs.
- `LinkSensorFunctions.hpp` to manage sensor linking, including creating, updating, and deleting scan register files and parameter files, generating sensor registers, reading registered IDs, and resetting sensors. Using the configuration button by setting it to MAGENTA it is possible to link a sensor to the device and by setting it to CYAN it is possible to reset the device to the default values.
- `RoutineSensorFunctions.hpp` for scheduled sensor routines, including sending ID requests, reading sensor data with configurable factors, updating parameter values, and handling routine callbacks.

### Changed
- Enhanced `updateScanRegisterFile()` in `LinkSensorFunctions.hpp` to include parameter names in the scan register.
- Improved `readSensorData()` in `RoutineSensorFunctions.hpp` to utilize the factor from the JSON parameter file for accurate value calculation.

### Fixed
- Corrected file handling logic in `RoutineSensorFunctions.hpp` to ensure parameter files are properly read and updated during routine callbacks.

This update significantly improves the RS485 handling capabilities and sensor management, providing robust functionalities for creating, updating, and deleting sensor-related files. The scheduled sensor routines are now more accurate and configurable, ensuring precise sensor data readings and updates.

## [0.0.4] - 2024-10-17

### Added
- `SleepConfig.hpp` with configuration for sleep mode, including constants and function prototypes.
- `SleepFunctions.hpp` with functions to manage the sleep mode, including entering deep sleep, checking sleep conditions, managing sleep mode, and setting sleep mode status.
- Interrupt handler `checkButtonPress()()` to toggle the sleep mode state when a button is pressed.

This update introduces sleep mode handling functionality, allowing the device to enter deep sleep mode based on a timer or a button configuration press, and wake up on a GPIO interrupt or timer. The functionality can be enabled or disabled dynamically, enhancing the device's power management capabilities.

## [0.1.0] - 2024-10-19

### Added
- `LoRaWANConfig.hpp` to configure LoRaWAN serial communication, including baud rate and pins for RX/TX.
- `LoRaWANFunctions.hpp` with functions to handle LoRaWAN communication, including `sendATCommand()`, `checkATResponse()`, and `initalizeLoRaWAN()`.
- `JoinConfig.hpp` with configuration for network join process, including connection status and timing constants.
- `JoinFunctions.hpp` to handle LoRaWAN network connection, including `checkNetworkConnection()` and `joinNetworkIfNeeded()`.
- `DownlinkConfig.hpp` for handling downlink payloads, including flags and constants for payload management.
- `DownlinkFunctions.hpp` with functions to process downlink commands, convert payloads to byte arrays, and store offset values.
- `UplinkConfig.hpp` for configuring uplink message settings, including timing intervals and payload size.
- `UplinkFunctions.hpp` with functions to build uplink payloads, check sensor state, and send uplink messages periodically.
- `CredentialsConfig.hpp` for storing LoRaWAN credentials such as `APPEUI`, `DEVEUI`, and `APPKEY`.
- `CredentialsFunctions.hpp` with functions to load, generate, and update LoRaWAN credentials, as well as send them to the LoRaWAN module.

This update adds comprehensive LoRaWAN handling functionality, including uplink and downlink message management, sensor data processing, and network connection management. The new `CredentialsConfig.hpp` and `CredentialsFunctions.hpp` enable dynamic loading and updating of LoRaWAN credentials, allowing seamless communication with LoRaWAN networks.
