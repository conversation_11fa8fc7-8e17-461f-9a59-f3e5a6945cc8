// src\AllNode\LoRaWAN\DownlinkFunctions.hpp
#ifndef DOWNLINKFUNCTIONS_HPP
#define DOWNLINKFUNCTIONS_HPP

#include "../Common/FileRecoveryFunctions.hpp"

bool writeOffsetSensor(uint8_t slaveAddress, const String &parameterFilePath, float &value)
{
    Serial.print("Reading sensor data for slave: ");
    Serial.println(slaveAddress);

    JsonDocument doc;
    File file = SD.open(parameterFilePath, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (!error)
        {
            Serial.print("Deserialized parameter file: ");
            Serial.println(parameterFilePath);

            uint16_t offsetAddress = strtol(doc["offset_address"], nullptr, 0);
            Serial.print("Using register address: ");
            Serial.println(offsetAddress, HEX);

            float factor = doc["factor"] | 1.0;
            int16_t scaledData = static_cast<int16_t>(value * factor);

            uint8_t msb = (scaledData >> 8) & 0xFF;
            uint8_t lsb = scaledData & 0xFF;

            uint8_t request[8] = {slaveAddress, 0x06, highByte(offsetAddress), lowByte(offsetAddress), msb, lsb};
            uint16_t crc = calculateCRC(request, 6);
            request[6] = lowByte(crc);
            request[7] = highByte(crc);

            Serial.print("Request offset: ");
            for (int i = 0; i < 8; i++)
            {
                Serial.print(request[i], HEX);
                Serial.print(" ");
            }
            Serial.println();

            rs485Serial.write(request, 8);

            delay(300);
            if (rs485Serial.available())
            {
                uint8_t response[8];
                for (uint8_t i = 0; i < 8; i++)
                {
                    response[i] = rs485Serial.read();
                }

                uint16_t crcResponse = calculateCRC(response, 6);
                if (response[0] == slaveAddress && crcResponse == (response[7] << 8 | response[6]))
                {
                    int16_t rawData = (response[4] << 8) | response[5];
                    Serial.print("Read sensor data successfully: ");
                    Serial.println(static_cast<float>(rawData) / factor);
                    return true;
                }
                else
                {
                    Serial.print("Invalid CRC response when reading sensor data from slave: ");
                    Serial.println(slaveAddress);
                }
            }
            else
            {
                Serial.print("No response when reading sensor data from slave: ");
                Serial.println(slaveAddress);
            }
        }
        else
        {
            Serial.print("Failed to deserialize parameter file: ");
            Serial.println(parameterFilePath);
        }
    }
    else
    {
        Serial.print("Failed to open parameter file: ");
        Serial.println(parameterFilePath);
    }
    return false;
}

void processLoRaWANResponse(String response)
{
    int startIndex = response.indexOf("+EVT:");

    String responseEVT = response.substring(startIndex + 1);

    Serial.print("Response EVT: ");
    Serial.println(responseEVT);

    if (startIndex != -1)
    {
        int colonIndex1 = responseEVT.indexOf(':');
        int colonIndex2 = responseEVT.indexOf(':', colonIndex1 + 1);
        int colonIndex3 = responseEVT.indexOf(':', colonIndex2 + 1);

        if (colonIndex3 != -1)
        {
            String sizeString = responseEVT.substring(colonIndex2 + 1, colonIndex3);

            Serial.print("SizeString payload: ");
            Serial.println(sizeString);

            int size = strtol(sizeString.c_str(), NULL, 16);

            Serial.print("Size payload: ");
            Serial.println(size);

            if (size > 0)
            {
                int payloadEndIndex = colonIndex3 + 1 + (size * 2);
                downlinkPayload = responseEVT.substring(colonIndex3 + 1, payloadEndIndex);

                downlinkReceivedFlag = true;

                Serial.print("Downlink payload received: ");
                Serial.println(downlinkPayload);
            }
            else
            {
                Serial.println("Invalid payload size.");
            }
        }
    }
    else
    {
        Serial.println("No downlink payload in the response.");
    }
}

void convertStringToByteArray(String payload, uint8_t *byteArray, uint8_t &length)
{
    // Inicializar la longitud del array
    length = 0;

    // Asegurarse de que el tamaño del payload no exceda el tamaño máximo permitido
    int payloadLength = payload.length();
    if (payloadLength > PAYLOAD_MAX_LENGTH * 2)
    {
        payloadLength = PAYLOAD_MAX_LENGTH * 2; // Truncar si es necesario
    }

    // Procesar cada par de caracteres del string y convertirlo en un byte
    for (int i = 0; i < payloadLength; i += 2)
    {
        String byteString = payload.substring(i, i + 2);                   // Obtener el par de caracteres
        byteArray[length] = (uint8_t)strtol(byteString.c_str(), NULL, 16); // Convertir de hexadecimal a byte
        length++;                                                          // Incrementar la longitud del array
    }
}

bool storeOffsetInParameter(uint8_t idSensor, const String &parameterName, float offset)
{
    String filePath = "/sensor_" + String(idSensor) + "_" + parameterName + ".json";
    JsonDocument doc;

    // Leer el archivo de forma segura con recuperación automática
    if (!safeReadJsonFile(filePath.c_str(), doc))
    {
        Serial.print("Error al leer el archivo del sensor: ");
        Serial.println(filePath);
        return false;
    }

    // Actualizar el offset
    doc["offset"] = offset;

    // Escribir el archivo de forma segura
    if (safeWriteJsonFile(filePath.c_str(), doc))
    {
        Serial.print("Offset guardado correctamente en: ");
        Serial.println(filePath);
        return true;
    }
    else
    {
        Serial.print("Error al escribir el archivo del sensor: ");
        Serial.println(filePath);
        return false;
    }
}

float decodeOffset(uint8_t sign, int16_t value, uint8_t idSensor, const String &parameterName)
{
    String filePath = "/sensor_" + String(idSensor) + "_" + parameterName + ".json";
    JsonDocument doc;
    File file = SD.open(filePath, FILE_READ);
    if (!file)
    {
        Serial.print("Error al abrir el archivo del sensor: ");
        Serial.println(filePath);
        return 0;
    }

    DeserializationError error = deserializeJson(doc, file);
    file.close();
    if (error)
    {
        Serial.print("Error al deserializar el archivo del sensor: ");
        Serial.println(filePath);
        return 0;
    }

    float factor = doc["factor"].as<float>();

    float decodedValue = value / factor;
    if (sign == 0x01)
    {
        decodedValue = -decodedValue;
    }

    Serial.print("Offset decodificado para ");
    Serial.print(parameterName);
    Serial.print(": ");
    Serial.println(decodedValue);

    return decodedValue;
}

JsonObject findSensorById(JsonDocument &doc, uint8_t idSensor)
{
    JsonArray ids = doc["ids"].as<JsonArray>();
    for (JsonObject sensor : ids)
    {
        if (sensor["id"] == idSensor)
        {
            return sensor;
        }
    }
    return JsonObject();
}

JsonDocument readScanRegisterLoRaWAN()
{
    JsonDocument doc;
    File file = SD.open(SCAN_REGISTER_PATH, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (error)
        {
            Serial.println("Error al deserializar scan_register.json.");
        }
    }
    else
    {
        Serial.println("Error al abrir scan_register.json.");
    }
    return doc;
}

void processOffsetsFromCommand()
{
    uint8_t index = 0; // Índice actual en el payload recibido

    while (index < receivedPayloadLength)
    {
        uint8_t commandType = receivedPayload[index++];

        if (commandType != 0x01)
        {
            Serial.println("Comando no reconocido, se espera un comando de tipo offset.");
            return; // Solo procesamos el comando de offset, otros tipos no son reconocidos.
        }

        while (index < receivedPayloadLength)
        {
            uint8_t idSensor = receivedPayload[index++];
            Serial.print("Procesando ID de sensor: ");
            Serial.println(idSensor);

            // Buscar en scan_register.json el idSensor
            JsonDocument scanRegister = readScanRegisterLoRaWAN();
            JsonObject sensorInfo = findSensorById(scanRegister, idSensor);

            if (sensorInfo.isNull())
            {
                Serial.print("No se encontró información para el sensor ID: ");
                Serial.println(idSensor);
                continue;
            }

            uint8_t typeSensor = sensorInfo["type"];
            JsonArray parameters = sensorInfo["parameters"].as<JsonArray>();

            for (size_t i = 0; i < parameters.size(); ++i)
            {
                if (index + 3 > receivedPayloadLength)
                {
                    Serial.println("Error: el payload no tiene suficientes datos para los parámetros esperados.");
                    return;
                }

                String parameterName = parameters[i].as<String>();
                Serial.print("Procesando parámetro: ");
                Serial.println(parameterName);

                // Leer los 3 bytes del offset
                uint8_t sign = receivedPayload[index++];
                uint8_t lsb = receivedPayload[index++];
                uint8_t msb = receivedPayload[index++];

                // Convertir el valor a decimal
                int16_t offsetValue = ((lsb << 8) | msb);
                float decodedOffset = decodeOffset(sign, offsetValue, idSensor, parameterName);

                // Guardar el offset en el JSON del parámetro
                if (!storeOffsetInParameter(idSensor, parameterName, decodedOffset))
                {
                    Serial.println("Error al guardar el offset en el archivo JSON.");
                }

                if (typeSensor == 1 || typeSensor == 2 || typeSensor == 3)
                {
                    Serial.println("Set offset for command RS485");
                    String filePath = "/sensor_" + String(idSensor) + "_" + parameterName + ".json";
                    if (writeOffsetSensor(idSensor, filePath, decodedOffset))
                    {
                        Serial.println("Offset OK");
                    }
                }
            }
        }
    }
    Serial.println("Comando procesado exitosamente.");
}

void processCommandLoRaWAN()
{
    if (receivedPayloadLength > 0)
    {
        Serial.println(F("Processing received command..."));

        switch (receivedPayload[0])
        {
        case ID_COMMAND:
            Serial.println(F("Received command: Offset command received"));
            processOffsetsFromCommand();
            break;

        default:
            Serial.println(F("Received command: Unknown command"));
            break;
        }

        receivedPayloadLength = 0;
    }
    else
    {
        Serial.println(F("No command to process"));
    }
}

void downlinkProcessingLoRaWAN()
{
    if (downlinkReceivedFlag)
    {
        Serial.print("Processing Downlink payload: ");
        Serial.println(downlinkPayload);

        // Convertir el payload recibido en un array de bytes
        convertStringToByteArray(downlinkPayload, receivedPayload, receivedPayloadLength);

        // Imprimir el array de bytes recibido
        Serial.print("Converted byte array: ");
        for (uint8_t i = 0; i < receivedPayloadLength; i++)
        {
            Serial.print(receivedPayload[i], HEX);
            Serial.print(" ");
        }
        Serial.println();

        processCommandLoRaWAN();

        downlinkReceivedFlag = false;
    }
}

#endif // DOWNLINKFUNCTIONS_HPP