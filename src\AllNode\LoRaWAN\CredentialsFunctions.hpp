// src\AllNode\LoRaWAN\CredentialsFunctions.hpp
#ifndef CREDENTIALSFUNCTIONS_HPP
#define CREDENTIALSFUNCTIONS_HPP

bool loadLoRaWANCredentials()
{
    File file = SD.open(LORAWAN_CREDENTIALS_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open lorawan_credentials.json");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse lorawan_credentials.json");
        return false;
    }

    lorawanCredentials.appeui = doc["APPEUI"].as<String>();
    lorawanCredentials.deveui = doc["DEVEUI"].as<String>();
    lorawanCredentials.appkey = doc["APPKEY"].as<String>();

    Serial.println("LoRaWAN credentials loaded successfully:");
    Serial.println("APPEUI: " + lorawanCredentials.appeui);
    Serial.println("DEVEUI: " + lorawanCredentials.deveui);
    Serial.println("APPKEY: " + lorawanCredentials.appkey);

    return true;
}

void updateDevEUIinJson()
{
    File file = SD.open(LORAWAN_CREDENTIALS_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open lorawan_credentials.json for updating DevEUI");
        return;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse lorawan_credentials.json for updating DevEUI");
        return;
    }

    doc["DEVEUI"] = lorawanCredentials.deveui;

    file = SD.open(LORAWAN_CREDENTIALS_PATH, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open lorawan_credentials.json for writing");
        return;
    }

    serializeJson(doc, file);
    file.close();
    Serial.println("Updated DevEUI in lorawan_credentials.json");
}

void generateAndSaveDevEUI()
{
    uint64_t chipid = ESP.getEfuseMac(); // Obtener la MAC de 6 bytes del ESP32
    uint8_t macBytes[6];
    for (int i = 0; i < 6; i++)
    {
        macBytes[i] = (chipid >> (8 * i)) & 0xFF;
    }

    // Crear el DevEUI asegurando que cada byte tenga dos dígitos
    lorawanCredentials.deveui =
        (macBytes[5] < 16 ? "0" : "") + String(macBytes[5], HEX) + ":" +
        (macBytes[4] < 16 ? "0" : "") + String(macBytes[4], HEX) + ":" +
        (macBytes[3] < 16 ? "0" : "") + String(macBytes[3], HEX) + ":" +
        "FF:FE:" + // Los bytes intermedios 0xFF y 0xFE son constantes
        (macBytes[2] < 16 ? "0" : "") + String(macBytes[2], HEX) + ":" +
        (macBytes[1] < 16 ? "0" : "") + String(macBytes[1], HEX) + ":" +
        (macBytes[0] < 16 ? "0" : "") + String(macBytes[0], HEX);

    lorawanCredentials.deveui.toUpperCase(); // Convertir a mayúsculas

    // Guardar el DevEUI en el archivo JSON
    updateDevEUIinJson();

    Serial.println("Generated and saved DevEUI: " + lorawanCredentials.deveui);
}

bool sendLoRaWANCommand(String command)
{
    Serial.println("Sending command: " + command);
    String response = sendATCommand(command.c_str(), TIME_CREDENTIALS);
    if (checkATResponse(response, "OK"))
    {
        Serial.println("Command executed successfully.");
        return true;
    }
    else
    {
        Serial.println("Error executing command.");
        return false;
    }
}

bool sendLoRaWANResetCommand(String command)
{
    Serial.println("Sending command: " + command);
    String response = sendATCommand(command.c_str(), TIME_RESET);
    if (checkATResponse(response, "OK"))
    {
        Serial.println("Command executed successfully.");
        return true;
    }
    else
    {
        Serial.println("Error executing command.");
        return true;
    }
}

void sendResetLoRaWAN()
{
    String command = "ATZ";
    if (sendLoRaWANResetCommand(command))
    {
        Serial.println("Reset successfully");
    }
}

void sendAppEUIToLoRaWAN()
{
    String command = "AT+APPEUI=" + lorawanCredentials.appeui;
    sendLoRaWANCommand(command);
}

void sendAppKeyToLoRaWAN()
{
    String command = "AT+NWKKEY=" + lorawanCredentials.appkey;
    sendLoRaWANCommand(command);
}

void sendDevEUIToLoRaWAN()
{
    String command = "AT+DEUI=" + lorawanCredentials.deveui;
    sendLoRaWANCommand(command);
}

void setupCredentialsLoRaWAN()
{
    if (!sendLoRaWANCommand("AT"))
    {
        Serial.println("LoRaWAN module not responding.");
        return;
    }

    generateAndSaveDevEUI();

    sendResetLoRaWAN();

    if (!sendLoRaWANCommand("AT"))
    {
        Serial.println("LoRaWAN module not responding.");
        return;
    }

    if (loadLoRaWANCredentials())
    {
        sendAppEUIToLoRaWAN();
        sendAppKeyToLoRaWAN();
        sendDevEUIToLoRaWAN();

        ESP.restart();
    }
    else
    {
        Serial.println("Failed to load LoRaWAN credentials.");
    }
}

#endif // CREDENTIALSFUNCTIONS_HPP
