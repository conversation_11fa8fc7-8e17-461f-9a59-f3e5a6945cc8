import subprocess
import os
from datetime import datetime

try:
    # Determine the latest release semver
    latest_release_tag = subprocess.run(["git", "describe", "--tags"], stdout=subprocess.PIPE, text=True)
    latest_release_tag = latest_release_tag.stdout.strip()
    if not latest_release_tag:
        raise ValueError("No tags found in the repository")
except subprocess.CalledProcessError as e:
    print(f"Error retrieving tags: {e}")
    latest_release_tag = "v0.0.0"

latest_release_semver = latest_release_tag.split("-")[0]
latest_release_digits = latest_release_semver.split(".")
latest_release_main = latest_release_digits[0]
latest_release_minor = latest_release_digits[1]
latest_release_patch = latest_release_digits[2]

# Determine current commit hash
current_commit_hash = subprocess.run(["git", "rev-parse", "--short", "HEAD"], stdout=subprocess.PIPE, text=True)
current_commit_hash = current_commit_hash.stdout.strip()

# Determine the build timestamp
build_timestamp = datetime.now().strftime("%Y-%b-%d %H:%M:%S")

# Store the results in a source file
include_file = open('lib/version/buildinfo.hpp', 'w')
include_file.write(f"const char* latestBuildTag    = \"{latest_release_tag}\";\n")
include_file.write(f"const int mainVersionDigit   = {latest_release_main};\n")
include_file.write(f"const int minorVersionDigit  = {latest_release_minor};\n")
include_file.write(f"const int patchVersionDigit  = {latest_release_patch};\n")
include_file.write(f"const char* lastCommitTag    = \"{current_commit_hash}\";\n")
include_file.write(f"const char* buildTimeStamp   = \"{build_timestamp}\";\n")
include_file.close()

# Export variables for the pipeline
print(f"::set-output name=latest_semver::{latest_release_semver}")
print(f"::set-output name=commit_hash::{current_commit_hash}")
