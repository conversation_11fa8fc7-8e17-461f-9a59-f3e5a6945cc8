// src\InitializeModule.hpp
#include "../lib/version/buildinfo.hpp"

#include "AllModule/RGB/RGBFunctions.hpp"
#include "AllModule/SD/SDFunctions.hpp"
#include "AllNode/RS485/RS485Functions.hpp"

#include "AllNode/RS485/LinkSensorFunctions.hpp"
#include "AllNode/RS485/RoutineSensorFunctions.hpp"
#include "AllNode/Sleep/SleepFunctions.hpp"

#include "AllNode/LoRaWAN/LoRaWANFunctions.hpp"

#include "AllModule/Button/ButtonFunctions.hpp"

void initializeModule()
{
    Serial.begin(115200);
    while (!Serial)
        ;
    Serial.println("Firmware Version: " + String(latestBuildTag));

    pinMode(ACTIVATION_PIN, OUTPUT);
    digitalWrite(ACTIVATION_PIN, HIGH);

    delay(500);

    // RS485
    initializeRS485();

    // RGB
    initializeRGB();
    setColor(BLACK);

    // BUTTON
    initializeButton();

    // SD
    SPI.begin(SCK_PIN, MISO_PIN, MOSI_PIN);
    initializeSD();

    // SPIFFS
    if (!SPIFFS.begin(true))
    {
        Serial.println(F("An Error has occurred while mounting SPIFFS"));
        return;
    }

    // SLEEP MODE
    initializeSleepConfig();

    // UPLINK CONFIG
    initializeUplinkConfig();

    // ROUTINE SENSOR
    tSensorRoutine.enable();

    // CREDENTIALS LORAWAN
    if (!loadLoRaWANCredentials())
    {
        Serial.println("Failed to load LoRaWAN credentials.");
    }
    // LORAWAN
    initalizeLoRaWAN();
}

void loopModule()
{
    delayUplinkMessageLoRaWAN();
    downlinkProcessingLoRaWAN();
    manageSleepMode();
    checkButtonPress();
    runner.execute();
}