// src\AllNode\LoRaWAN\UplinkConfig.hpp
#ifndef UPLINKCONFIG_HPP
#define UPLINKCONFIG_HPP

#define UPLINK_CONFIG_PATH "/uplink_config.json"

#define PAYLOAD_MAX_SIZE 50

unsigned long previousMillisLoRaWAN = 0;

bool taskEnabledFlag = false;

struct UplinkConfig
{
    unsigned long intervalLoRaWAN;
};

UplinkConfig uplinkConfig = {60};

#define TIME_UPLINK 2000

void uplinkMessageLoRaWAN();

Task tUplinkMessagesLoRaWAN(TASK_SECOND * 60, TASK_FOREVER, &uplinkMessageLoRaWAN, &runner);

#endif // UPLINKCONFIG_HPP