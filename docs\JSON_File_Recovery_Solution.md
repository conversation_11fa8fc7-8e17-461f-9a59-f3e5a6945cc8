# Solución para Recuperación de Archivos JSON Corruptos

## Problema Identificado

El sistema presentaba el siguiente comportamiento problemático:

```
Starting sensor routine callback
Sending ID request to slave: 4
Received valid response from slave: 4
Failed to deserialize parameter file: /sensor_4_Temperature.json
Humidity 4: 71.60 % Offset: 0.00
Sensor routine callback completed
```

**Causa raíz**: Cuando ocurría un error durante la escritura de archivos JSON (por ejemplo, por falta de energía, interrupción del proceso, o problemas de SD), los archivos quedaban vacíos o corruptos, perdiendo su estructura original y causando fallos en iteraciones posteriores.

## Solución Implementada

### 1. Sistema de Backup Automático

- **Backup antes de modificar**: Cada vez que se va a modificar un archivo JSON, se crea automáticamente un backup con extensión `.bak`
- **Validación de backups**: Los backups se validan antes de usarlos para asegurar que contienen JSON válido

### 2. Escritura Segura con Archivos Temporales

- **Archivo temporal**: Se escribe primero a un archivo `.tmp`
- **Validación**: Se verifica que el archivo temporal contiene JSON válido
- **Reemplazo atómico**: Solo si la validación es exitosa, se reemplaza el archivo original

### 3. Recuperación Automática

#### Recuperación desde Backup
```cpp
bool recoverParameterFileFromBackup(const char *filePath)
```
- Busca un archivo `.bak` válido
- Si existe y es válido, lo restaura como archivo principal
- Si no hay backup válido, procede a la recreación desde DB

#### Recreación desde Base de Datos
```cpp
bool recreateParameterFileFromDB(const char *filePath)
```
- Extrae el ID del sensor y nombre del parámetro del path del archivo
- Consulta `scan_register.json` para obtener el tipo de sensor
- Consulta `sensor_db.json` para obtener la estructura original del parámetro
- Recrea el archivo con valores por defecto pero estructura correcta

### 4. Funciones Seguras de E/O

#### Lectura Segura
```cpp
bool safeReadJsonFile(const char *filePath, JsonDocument &doc)
```
- Intenta leer el archivo
- Si falla la deserialización, activa recuperación automática
- Reintenta la lectura después de la recuperación

#### Escritura Segura
```cpp
bool safeWriteJsonFile(const char *filePath, JsonDocument &doc)
```
- Crea backup del archivo original
- Escribe a archivo temporal
- Valida el archivo temporal
- Reemplaza el original solo si la validación es exitosa

## Archivos Modificados

### 1. `src/AllNode/Common/FileRecoveryFunctions.hpp` (NUEVO)
Contiene todas las funciones de recuperación y escritura segura de archivos JSON.

### 2. `src/AllNode/RS485/RoutineSensorFunctions.hpp`
- Función `updateParameterValue()` simplificada para usar funciones seguras
- Incluye el nuevo archivo de cabecera

### 3. `src/AllNode/LoRaWAN/DownlinkFunctions.hpp`
- Función `storeOffsetInParameter()` simplificada para usar funciones seguras
- Incluye el nuevo archivo de cabecera

### 4. `src/AllNode/LoRaWAN/UplinkFunctions.hpp`
- Función `readSensorData()` simplificada para usar funciones seguras
- Incluye el nuevo archivo de cabecera

## Beneficios de la Solución

### 1. **Resistencia a Fallos**
- Los archivos JSON nunca quedan en estado corrupto permanentemente
- Sistema de backup automático previene pérdida de datos

### 2. **Recuperación Automática**
- No requiere intervención manual cuando ocurren errores
- Múltiples niveles de recuperación (backup → recreación desde DB)

### 3. **Integridad de Datos**
- Validación de JSON antes de confirmar escrituras
- Escritura atómica previene corrupción durante el proceso

### 4. **Mantenibilidad**
- Código centralizado en un solo archivo de cabecera
- Funciones reutilizables en todo el proyecto
- Logging detallado para debugging

## Flujo de Recuperación

```mermaid
graph TD
    A[Intento de lectura JSON] --> B{¿Archivo existe?}
    B -->|No| C[Intentar recuperar desde backup]
    B -->|Sí| D{¿Deserialización exitosa?}
    D -->|Sí| E[Lectura exitosa]
    D -->|No| C
    C --> F{¿Backup válido?}
    F -->|Sí| G[Restaurar desde backup]
    F -->|No| H[Recrear desde sensor DB]
    G --> I[Reintentar lectura]
    H --> I
    I --> J{¿Lectura exitosa?}
    J -->|Sí| E
    J -->|No| K[Error: No se pudo recuperar]
```

## Uso en el Código

### Antes (Problemático)
```cpp
JsonDocument doc;
File file = SD.open(filePath, FILE_READ);
if (file) {
    DeserializationError error = deserializeJson(doc, file);
    file.close();
    if (error) {
        // Error sin recuperación
        return false;
    }
}
```

### Después (Seguro)
```cpp
JsonDocument doc;
if (!safeReadJsonFile(filePath, doc)) {
    // Error con recuperación automática ya intentada
    return false;
}
// El documento está garantizado como válido
```

## Consideraciones de Rendimiento

- **Overhead mínimo**: Solo se activa la recuperación cuando hay errores
- **Espacio en SD**: Archivos `.bak` y `.tmp` temporales (se limpian automáticamente)
- **Tiempo adicional**: Solo durante escrituras (creación de backup y validación)

## Monitoreo y Debugging

El sistema proporciona logging detallado:
- `"Recovered parameter file from backup: [path]"`
- `"Recreated parameter file from DB: [path]"`
- `"Successfully recovered and read file: [path]"`
- `"Recovery failed, file still corrupted"`
- `"File recovery failed"`

Esto permite monitorear la salud del sistema de archivos y identificar problemas recurrentes.
