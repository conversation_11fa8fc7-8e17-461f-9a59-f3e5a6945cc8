// src\AllModule\RGB\RGBFunctions.hpp
#ifndef RGBFUNCTIONS_HPP
#define RGBFUNCTIONS_HPP

enum Color
{
    BLACK,
    RED,
    GREEN,
    BLUE,
    MAGENTA,
    YELLOW,
    CYAN,
    WHITE
};

void initializeRGB()
{
    pixels.begin();
    pixels.show();
}

void setColor(Color color)
{
    switch (color)
    {
    case BLACK:
        pixels.setPixelColor(0, pixels.Color(0, 0, 0));
        pixels.show();
        break;
    case RED:
        pixels.setPixelColor(0, pixels.Color(255, 0, 0));
        pixels.show();
        break;
    case GREEN:
        pixels.setPixelColor(0, pixels.Color(0, 255, 0));
        pixels.show();
        break;
    case BLUE:
        pixels.setPixelColor(0, pixels.Color(0, 0, 255));
        pixels.show();
        break;
    case MAGENTA:
        pixels.setPixelColor(0, pixels.Color(255, 0, 255));
        pixels.show();
        break;
    case YELLOW:
        pixels.setPixelColor(0, pixels.Color(255, 255, 0));
        pixels.show();
        break;
    case CYAN:
        pixels.setPixelColor(0, pixels.Color(0, 255, 255));
        pixels.show();
        break;
    case WHITE:
        pixels.setPixelColor(0, pixels.Color(255, 255, 255));
        pixels.show();
        break;
    }
}

void cycleColors()
{
    for (int color = BLACK; color <= WHITE; color++)
    {
        setColor(static_cast<Color>(color));
        delay(1000);
    }
}

#endif // src\AllModule\RGB\RGBFunctions.hpp