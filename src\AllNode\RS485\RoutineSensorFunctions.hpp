// src\AllNode\RS485\RoutineSensorFunctions.hpp
#ifndef ROUTINE_SENSOR_FUNCTIONS_HPP
#define ROUTINE_SENSOR_FUNCTIONS_HPP

#include "../Common/FileRecoveryFunctions.hpp"

bool sendSlaveIDRequestRoutine(uint8_t slaveAddress, uint8_t readAddress, uint16_t idAddress)
{
    Serial.print("Sending ID request to slave: ");
    Serial.println(slaveAddress);

    // uint8_t request[8] = {slaveAddress, 0x03, 0x07, 0xD0, 0x00, 0x01};
    uint8_t request[8] = {slaveAddress, readAddress, highByte(idAddress), lowByte(idAddress), 0x00, 0x01};
    uint16_t crc = calculateCRC(request, 6);
    request[6] = lowByte(crc);
    request[7] = highByte(crc);

    rs485Serial.write(request, 8);

    delay(300);
    if (rs485Serial.available())
    {
        uint8_t response[7];
        for (uint8_t i = 0; i < 7; i++)
        {
            response[i] = rs485Serial.read();
        }

        uint16_t crcResponse = calculateCRC(response, 5);
        if (response[0] == slaveAddress && crcResponse == (response[6] << 8 | response[5]))
        {
            Serial.print("Received valid response from slave: ");
            Serial.println(slaveAddress);
            return true;
        }
        else
        {
            Serial.print("Invalid CRC response from slave: ");
            Serial.println(slaveAddress);
        }
    }
    else
    {
        Serial.print("No response from slave: ");
        Serial.println(slaveAddress);
    }
    return false;
}

bool readSensorData(uint8_t slaveAddress, uint8_t readAddress, const char *parameterFilePath, float &value)
{
    // Serial.print("Reading sensor data for slave: ");
    // Serial.println(slaveAddress);

    JsonDocument doc;
    File file = SD.open(parameterFilePath, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (!error)
        {
            // Serial.print("Deserialized parameter file: ");
            // Serial.println(parameterFilePath);

            uint16_t registerAddress = strtol(doc["register_address"], nullptr, 0);
            // Serial.print("Using register address: ");
            // Serial.println(registerAddress, HEX);

            float factor = doc["factor"] | 1.0;

            // uint8_t request[8] = {slaveAddress, 0x03, highByte(registerAddress), lowByte(registerAddress), 0x00, 0x01};
            uint8_t request[8] = {slaveAddress, readAddress, highByte(registerAddress), lowByte(registerAddress), 0x00, 0x01};
            uint16_t crc = calculateCRC(request, 6);
            request[6] = lowByte(crc);
            request[7] = highByte(crc);

            rs485Serial.write(request, 8);

            delay(300);
            if (rs485Serial.available())
            {
                uint8_t response[7];
                for (uint8_t i = 0; i < 7; i++)
                {
                    response[i] = rs485Serial.read();
                }

                uint16_t crcResponse = calculateCRC(response, 5);
                if (response[0] == slaveAddress && crcResponse == (response[6] << 8 | response[5]))
                {
                    uint16_t rawData = (response[3] << 8) | response[4];
                    value = static_cast<float>(rawData) / factor;
                    // Serial.print("Read sensor data successfully: ");
                    // Serial.println(value);
                    return true;
                }
                else
                {
                    Serial.print("Invalid CRC response when reading sensor data from slave: ");
                    Serial.println(slaveAddress);
                }
            }
            else
            {
                Serial.print("No response when reading sensor data from slave: ");
                Serial.println(slaveAddress);
            }
        }
        else
        {
            Serial.print("Failed to deserialize parameter file: ");
            Serial.println(parameterFilePath);
        }
    }
    else
    {
        Serial.print("Failed to open parameter file: ");
        Serial.println(parameterFilePath);
    }
    return false;
}



bool updateParameterValue(const char *filePath, float value)
{
    // Serial.print("Updating parameter value in file: ");
    // Serial.println(filePath);

    JsonDocument doc;

    // Leer el archivo de forma segura con recuperación automática
    if (!safeReadJsonFile(filePath, doc))
    {
        Serial.print("Failed to read file for updating: ");
        Serial.println(filePath);
        return false;
    }

    // Actualizar el valor
    doc["value"] = value;

    // Escribir el archivo de forma segura
    if (safeWriteJsonFile(filePath, doc))
    {
        // Serial.print("Updated value successfully in file: ");
        // Serial.println(filePath);
        return true;
    }
    else
    {
        Serial.print("Failed to write updated file: ");
        Serial.println(filePath);
        return false;
    }
}

void sensorRoutineCallback()
{
    Serial.println("Starting sensor routine callback");

    JsonDocument scanRegister;
    File file = SD.open(SCAN_REGISTER_PATH, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(scanRegister, file);
        file.close();
        if (!error)
        {
            JsonArray ids = scanRegister["ids"];
            for (JsonObject idEntry : ids)
            {
                uint8_t slaveAddress = idEntry["id"];
                uint8_t readAddress = idEntry["readRegister"];
                uint8_t writeAddress = idEntry["writeRegister"];
                uint16_t idAddress = strtol(idEntry["idRegister"], nullptr, 0);
                uint8_t typeSensor = idEntry["type"];
                if (sendSlaveIDRequestRoutine(slaveAddress, readAddress, idAddress))
                {
                    JsonArray parameters = idEntry["parameters"];
                    for (JsonVariant parameter : parameters)
                    {
                        String filePath = "/sensor_" + String(slaveAddress) + "_" + parameter.as<String>() + ".json";
                        float value = 0;
                        if (readSensorData(slaveAddress, readAddress, filePath.c_str(), value))
                        {
                            if (updateParameterValue(filePath.c_str(), value))
                            {
                                String name = parameter.as<String>();
                                String unit = "";
                                float offset = 0;

                                file = SD.open(filePath.c_str(), FILE_READ);
                                JsonDocument doc;
                                if (file)
                                {
                                    DeserializationError error = deserializeJson(doc, file);
                                    file.close();
                                    if (!error)
                                    {
                                        unit = doc["unit"].as<String>();
                                        offset = doc["offset"].as<float>();
                                    }
                                }

                                if (typeSensor == 1 || typeSensor == 2 || typeSensor == 3)
                                {
                                    Serial.print(name);
                                    Serial.print(" ");
                                    Serial.print(slaveAddress);
                                    Serial.print(": ");
                                    Serial.print(value);
                                    Serial.print(" ");
                                    Serial.print(unit);
                                    Serial.print(" Offset: ");
                                    Serial.println(offset);
                                }
                                else
                                {
                                    Serial.print(name);
                                    Serial.print(" ");
                                    Serial.print(slaveAddress);
                                    Serial.print(": ");
                                    Serial.print(value + offset);
                                    Serial.print(" ");
                                    Serial.print(unit);
                                    Serial.print(" Offset: ");
                                    Serial.println(offset);
                                }
                            }
                        }
                    }
                }
                delay(150);
            }
        }
        else
        {
            Serial.println("Failed to deserialize scan_register file");
        }
    }
    else
    {
        Serial.println("Failed to open scan_register file");
    }
    Serial.println("Sensor routine callback completed");
}

#endif // ROUTINE_SENSOR_FUNCTIONS_HPP
