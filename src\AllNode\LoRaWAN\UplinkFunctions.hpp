// src\AllNode\LoRaWAN\UplinkFunctions.hpp
#ifndef UPLINKFUNCTIONS_HPP
#define UPLINKFUNCTIONS_HPP

bool loadUplinkConfig(const char *path, UplinkConfig &config)
{
    File file = SD.open(path, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open sleep config file");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse sleep config file");
        return false;
    }

    config.intervalLoRaWAN = doc["intervalLoRaWAN"];

    return true;
}

JsonDocument readScanRegister()
{
    JsonDocument doc;
    File file = SD.open(SCAN_REGISTER_PATH, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (error)
        {
            Serial.println("Failed to deserialize scan_register file.");
        }
    }
    else
    {
        Serial.println("Failed to open scan_register file.");
    }
    return doc;
}

bool checkSensorState(uint8_t id, uint8_t readAddress, uint16_t idAddress)
{
    uint8_t request[8] = {id, readAddress, highByte(idAddress), lowByte(idAddress), 0x00, 0x01};
    uint16_t crc = calculateCRC(request, 6);
    request[6] = lowByte(crc);
    request[7] = highByte(crc);

    rs485Serial.write(request, 8);
    delay(100);

    if (rs485Serial.available())
    {
        uint8_t response[7];
        for (uint8_t i = 0; i < 7; i++)
        {
            response[i] = rs485Serial.read();
        }

        uint16_t crcResponse = calculateCRC(response, 5);
        return (response[0] == id && crcResponse == (response[6] << 8 | response[5]));
    }
    return false;
}

JsonDocument readSensorData(const String &filePath)
{
    JsonDocument doc;
    File file = SD.open(filePath, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (error)
        {
            Serial.print("Failed to deserialize file: ");
            Serial.println(filePath);
        }
    }
    else
    {
        Serial.print("Failed to open file: ");
        Serial.println(filePath);
    }
    return doc;
}

String buildPayload()
{
    JsonDocument scanRegister = readScanRegister();
    JsonArray ids = scanRegister["ids"];

    String payload = "";

    for (JsonObject idEntry : ids)
    {
        uint8_t idSensor = idEntry["id"];
        uint8_t typeSensor = idEntry["type"];
        uint8_t readAddress = idEntry["readRegister"];
        uint8_t writeAddress = idEntry["writeRegister"];
        uint16_t idAddress = strtol(idEntry["idRegister"], nullptr, 0);
        bool state = checkSensorState(idSensor, readAddress, idAddress);

        payload += String(idSensor < 16 ? "0" : "") + String(idSensor, HEX);
        payload += String(typeSensor < 16 ? "0" : "") + String(typeSensor, HEX);
        payload += state ? "01" : "00";

        if (state)
        {
            JsonArray paramNames = idEntry["parameters"];
            for (JsonVariant paramName : paramNames)
            {
                String filePath = "/sensor_" + String(idSensor) + "_" + paramName.as<String>() + ".json";
                JsonDocument sensorData = readSensorData(filePath.c_str());

                float data = 0;
                if (typeSensor == 1 || typeSensor == 2 || typeSensor == 3)
                {
                    data = sensorData["value"].as<float>();
                }
                else
                {
                    data = sensorData["value"].as<float>() + sensorData["offset"].as<float>();
                }

                float factor = sensorData["factor"].as<float>();
                uint16_t scaledData = static_cast<uint16_t>(data * factor);

                uint8_t msb = (scaledData >> 8) & 0xFF;
                uint8_t lsb = scaledData & 0xFF;

                payload += String(msb < 16 ? "0" : "") + String(msb, HEX);
                payload += String(lsb < 16 ? "0" : "") + String(lsb, HEX);
            }
        }
    }

    if (payload.length() > PAYLOAD_MAX_SIZE * 2)
    {
        Serial.println("Payload too long, truncating.");
        payload = payload.substring(0, PAYLOAD_MAX_SIZE * 2);
    }

    Serial.print("Payload built: ");
    Serial.println(payload);

    return payload;
}

void uplinkMessageLoRaWAN()
{
    if (connectedToNetwork)
    {
        String payload = buildPayload();

        String command = "AT+SEND=1:0:" + payload;

        String response = sendATCommand(command.c_str(), TIME_UPLINK);
        if (checkATResponse(response, "OK"))
        {
            Serial.println("Message sent successfully.");
            processLoRaWANResponse(response);
        }
        else
        {
            Serial.println("Error sending message.");
        }
    }
    else
    {
        Serial.println("Not connected to the network. Message cannot be sent.");
    }
}

void delayUplinkMessageLoRaWAN()
{
    unsigned long currentMillisLoRaWAN = millis();

    if (connectedToNetwork && (currentMillisLoRaWAN - previousMillisLoRaWAN >= (uplinkConfig.intervalLoRaWAN * 1000)))
    {
        if (!taskEnabledFlag)
        {
            previousMillisLoRaWAN = currentMillisLoRaWAN;
            tUplinkMessagesLoRaWAN.enable();
            taskEnabledFlag = true;
        }
    }
}

void initializeUplinkConfig()
{
    if (!loadUplinkConfig(UPLINK_CONFIG_PATH, uplinkConfig))
    {
        Serial.println("Failed to load uplink config. Using default values.");
    }
    else
    {
        Serial.println("Uplink config loaded successfully.");
    }
}

#endif // UPLINKFUNCTIONS_HPP