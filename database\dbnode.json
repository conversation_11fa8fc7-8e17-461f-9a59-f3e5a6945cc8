{"sensors": [{"id": 1, "name": "THC-S", "type": "Soil", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GSX01", "default_name": "Soil Sensor THC-S", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0001", "offset_address": "0x0050"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "unit": "%", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0000", "offset_address": "0x0051"}, {"name": "Conductivity", "unit": "us/cm", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0002", "offset_address": "0x0052"}]}, {"id": 2, "name": "THCPH-S", "type": "Soil", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GSX02", "default_name": "Soil Sensor THCPH-S", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0001", "offset_address": "0x0050"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "unit": "%", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0000", "offset_address": "0x0051"}, {"name": "Conductivity", "unit": "us/cm", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0002", "offset_address": "0x0052"}, {"name": "pH", "unit": "pH", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0003", "offset_address": "0x0053"}]}, {"id": 3, "name": "NPK-S", "type": "Soil", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GSX03", "default_name": "Soil Sensor NPK-S", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0001", "offset_address": "0x0050"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "unit": "%", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0000", "offset_address": "0x0051"}, {"name": "Conductivity", "unit": "us/cm", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0002", "offset_address": "0x0052"}, {"name": "pH", "unit": "pH", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0003", "offset_address": "0x0053"}, {"name": "Nitrogen", "unit": "mg/kg", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0004", "offset_address": "0x04EA"}, {"name": "Phosphorus", "unit": "mg/kg", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0005", "offset_address": "0x04F4"}, {"name": "Potassium", "unit": "mg/kg", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0006", "offset_address": "0x04FE"}]}, {"id": 4, "name": "SAQ-TH", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX01", "default_name": "Env Sensor SAQ-TH", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0003"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "unit": "%", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0002"}]}, {"id": 5, "name": "BY-TH", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX02", "default_name": "Env Sensor BY-TH", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F5"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "unit": "%", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F4"}]}, {"id": 6, "name": "PS", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX03", "default_name": "Env Sensor PS", "parameters": [{"name": "Photosynthetic", "unit": "umol/m2s", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0000"}]}, {"id": 7, "name": "SWC-C", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX04", "default_name": "Env Sensor SWC-C", "parameters": [{"name": "WindSpeed", "unit": "m/s", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0000"}]}, {"id": 8, "name": "RG", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX05", "default_name": "Env Sensor RG", "parameters": [{"name": "RainGauge", "unit": "mm", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0000"}]}, {"id": 9, "name": "BY-THCO2", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX06", "default_name": "Env Sensor BY-THCO2", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F5"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "unit": "%", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F4"}, {"name": "CO2", "unit": "ppm", "value": 0, "offset": 0, "factor": 1, "register_address": "0x01F6"}]}, {"id": 10, "name": "BY-THISCO2", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX07", "default_name": "Env Sensor BY-THISCO2", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F5"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "unit": "%", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F4"}, {"name": "CO2", "unit": "ppm", "value": 0, "offset": 0, "factor": 1, "register_address": "0x01F7"}]}, {"id": 11, "name": "SAQ-CO2", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX08", "default_name": "Env Sensor SAQ-CO2", "parameters": [{"name": "CO2", "unit": "ppm", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0008"}]}, {"id": 12, "name": "UWD", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX09", "default_name": "Env Sensor UWD", "parameters": [{"name": "WindSpeed", "unit": "m/s", "value": 0, "offset": 0, "factor": 100, "register_address": "0x01F4"}, {"name": "WindDirection", "unit": " ", "value": 0, "offset": 0, "factor": 1, "register_address": "0x01F6"}, {"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F9"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "unit": "%", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F8"}, {"name": "PM2.5", "unit": "ug/m3", "value": 0, "offset": 0, "factor": 1, "register_address": "0x01FB"}, {"name": "PM10", "unit": "ug/m3", "value": 0, "offset": 0, "factor": 1, "register_address": "0x01FC"}, {"name": "Noise", "unit": "dB", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01FA"}, {"name": "Pressure", "unit": "kpa", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01FD"}, {"name": "Rainfall", "unit": "mm", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0201"}]}, {"id": 13, "name": "SY-A0501", "type": "Water", "readRegister": 3, "writeRegister": 6, "idRegister": "0x0000", "code": "GWX01", "default_name": "Water Quality 5 in 1", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 100, "register_address": "0x0001"}, {"name": "pH", "unit": "pH", "value": 0, "offset": 0, "factor": 100, "register_address": "0x0002"}, {"name": "Salinity", "unit": " ", "value": 0, "offset": 0, "factor": 1000, "register_address": "0x0003"}, {"name": "Conductivity", "unit": "us/cm", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0004"}]}, {"id": 14, "name": "WLS", "type": "Water", "readRegister": 3, "writeRegister": 6, "idRegister": "0x0000", "code": "GWX02", "default_name": "Water Level Pressure", "parameters": [{"name": "HighLevel", "unit": " ", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0016"}, {"name": "LowLevel", "unit": " ", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0017"}]}, {"id": 15, "name": "ULS", "type": "Water", "readRegister": 3, "writeRegister": 6, "idRegister": "0x0001", "code": "GWX03", "default_name": "Water Level Ultrasonic", "parameters": [{"name": "Distance", "unit": "mm", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0001"}]}, {"id": 16, "name": "CWT-BL-PH", "type": "Water", "readRegister": 3, "writeRegister": 10, "idRegister": "0x0002", "code": "GWX04", "default_name": "Water PH", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0000"}, {"name": "pH", "unit": "pH", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0001"}]}, {"id": 17, "name": "CWT-BL-EC", "type": "Water", "readRegister": 3, "writeRegister": 10, "idRegister": "0x0002", "code": "GWX05", "default_name": "Water EC", "parameters": [{"name": "Conductivity", "unit": "us/cm", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0001"}]}, {"id": 18, "name": "CWT-OYS-PHEC", "type": "Water", "readRegister": 3, "writeRegister": 6, "idRegister": "0x0030", "code": "GWX06", "default_name": "Water PHEC", "parameters": [{"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x0002"}, {"name": "pH", "unit": "pH", "value": 0, "offset": 0, "factor": 100, "register_address": "0x0000"}, {"name": "Conductivity", "unit": "us/cm", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0001"}]}, {"id": 19, "name": "UWD2", "type": "Env", "readRegister": 3, "writeRegister": 6, "idRegister": "0x07D0", "code": "GEX10", "default_name": "Env Sensor UWD 2", "parameters": [{"name": "WindSpeed", "unit": "m/s", "value": 0, "offset": 0, "factor": 100, "register_address": "0x01F4"}, {"name": "WindDirection", "unit": " ", "value": 0, "offset": 0, "factor": 1, "register_address": "0x01F6"}, {"name": "Temperature", "unit": "°C", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F9"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "unit": "%", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01F8"}, {"name": "Pressure", "unit": "kpa", "value": 0, "offset": 0, "factor": 10, "register_address": "0x01FD"}, {"name": "Irradiance", "unit": "W/m2", "value": 0, "offset": 0, "factor": 1, "register_address": "0x0203"}]}]}