// src\AllNode\Common\FileRecoveryFunctions.hpp
#ifndef FILERECOVERYFUNCTIONS_HPP
#define FILERECOVERYFUNCTIONS_HPP

/**
 * @brief Recupera un archivo de parámetros desde su backup
 * @param filePath Ruta del archivo a recuperar
 * @return true si la recuperación fue exitosa, false en caso contrario
 */
bool recoverParameterFileFromBackup(const char *filePath)
{
    String backupPath = String(filePath) + ".bak";
    
    if (SD.exists(backupPath.c_str()))
    {
        // Intentar restaurar desde backup
        File backupFile = SD.open(backupPath.c_str(), FILE_READ);
        if (backupFile)
        {
            JsonDocument testDoc;
            DeserializationError error = deserializeJson(testDoc, backupFile);
            backupFile.close();
            
            if (!error)
            {
                // El backup es válido, copiarlo al archivo principal
                SD.remove(filePath); // Eliminar archivo corrupto
                
                backupFile = SD.open(backupPath.c_str(), FILE_READ);
                File mainFile = SD.open(filePath, FILE_WRITE);
                
                if (backupFile && mainFile)
                {
                    while (backupFile.available())
                    {
                        mainFile.write(backupFile.read());
                    }
                    backupFile.close();
                    mainFile.close();
                    
                    Serial.print("Recovered parameter file from backup: ");
                    Serial.println(filePath);
                    return true;
                }
            }
        }
    }
    
    // Si no hay backup válido, recrear estructura básica desde sensor DB
    return recreateParameterFileFromDB(filePath);
}

/**
 * @brief Recrea un archivo de parámetros desde la base de datos de sensores
 * @param filePath Ruta del archivo a recrear
 * @return true si la recreación fue exitosa, false en caso contrario
 */
bool recreateParameterFileFromDB(const char *filePath)
{
    // Extraer ID del sensor y nombre del parámetro del path
    String path = String(filePath);
    int sensorStart = path.indexOf("sensor_") + 7;
    int sensorEnd = path.indexOf("_", sensorStart);
    int paramStart = sensorEnd + 1;
    int paramEnd = path.indexOf(".json");
    
    if (sensorStart < 7 || sensorEnd < 0 || paramStart < 0 || paramEnd < 0)
    {
        Serial.println("Invalid file path format for recreation");
        return false;
    }
    
    uint8_t sensorId = path.substring(sensorStart, sensorEnd).toInt();
    String paramName = path.substring(paramStart, paramEnd);
    
    // Buscar en scan_register para obtener el tipo de sensor
    JsonDocument scanRegister;
    File file = SD.open(SCAN_REGISTER_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Cannot open scan_register.json for recreation");
        return false;
    }
    
    DeserializationError error = deserializeJson(scanRegister, file);
    file.close();
    if (error)
    {
        Serial.println("Cannot parse scan_register.json for recreation");
        return false;
    }
    
    uint8_t sensorType = 0;
    JsonArray ids = scanRegister["ids"];
    for (JsonObject idEntry : ids)
    {
        if (idEntry["id"] == sensorId)
        {
            sensorType = idEntry["type"];
            break;
        }
    }
    
    // Buscar en sensor DB para obtener la estructura del parámetro
    JsonDocument sensorDB;
    file = SD.open(SENSOR_DB_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Cannot open sensor_db.json for recreation");
        return false;
    }
    
    error = deserializeJson(sensorDB, file);
    file.close();
    if (error)
    {
        Serial.println("Cannot parse sensor_db.json for recreation");
        return false;
    }
    
    // Encontrar el parámetro en la base de datos
    for (JsonObject sensor : sensorDB["sensors"].as<JsonArray>())
    {
        if (sensor["id"] == sensorType)
        {
            for (JsonObject parameter : sensor["parameters"].as<JsonArray>())
            {
                if (parameter["name"].as<String>() == paramName)
                {
                    // Recrear el archivo con la estructura original
                    JsonDocument newDoc;
                    newDoc["name"] = parameter["name"];
                    newDoc["unit"] = parameter["unit"];
                    newDoc["register_address"] = parameter["register_address"];
                    newDoc["factor"] = parameter["factor"];
                    newDoc["offset"] = parameter["offset"];
                    newDoc["value"] = 0.0; // Valor por defecto
                    
                    file = SD.open(filePath, FILE_WRITE);
                    if (file)
                    {
                        serializeJson(newDoc, file);
                        file.close();
                        
                        Serial.print("Recreated parameter file from DB: ");
                        Serial.println(filePath);
                        return true;
                    }
                    break;
                }
            }
            break;
        }
    }
    
    Serial.print("Failed to recreate parameter file: ");
    Serial.println(filePath);
    return false;
}

/**
 * @brief Crea un backup de un archivo antes de modificarlo
 * @param filePath Ruta del archivo a respaldar
 * @return true si el backup fue creado exitosamente, false en caso contrario
 */
bool createFileBackup(const char *filePath)
{
    String backupPath = String(filePath) + ".bak";
    File originalFile = SD.open(filePath, FILE_READ);
    File backupFile = SD.open(backupPath.c_str(), FILE_WRITE);
    
    if (originalFile && backupFile)
    {
        while (originalFile.available())
        {
            backupFile.write(originalFile.read());
        }
        originalFile.close();
        backupFile.close();
        return true;
    }
    
    if (originalFile) originalFile.close();
    if (backupFile) backupFile.close();
    return false;
}

/**
 * @brief Escribe un archivo JSON de forma segura usando archivo temporal
 * @param filePath Ruta del archivo a escribir
 * @param doc Documento JSON a escribir
 * @return true si la escritura fue exitosa, false en caso contrario
 */
bool safeWriteJsonFile(const char *filePath, JsonDocument &doc)
{
    // Crear backup antes de modificar
    createFileBackup(filePath);
    
    // Escribir a archivo temporal primero
    String tempPath = String(filePath) + ".tmp";
    File file = SD.open(tempPath.c_str(), FILE_WRITE);
    if (!file)
    {
        Serial.print("Failed to open temporary file for writing: ");
        Serial.println(tempPath);
        return false;
    }

    serializeJson(doc, file);
    file.close();
    
    // Verificar que el archivo temporal es válido
    file = SD.open(tempPath.c_str(), FILE_READ);
    if (file)
    {
        JsonDocument testDoc;
        DeserializationError testError = deserializeJson(testDoc, file);
        file.close();
        
        if (!testError)
        {
            // El archivo temporal es válido, reemplazar el original
            SD.remove(filePath);
            
            File tempFile = SD.open(tempPath.c_str(), FILE_READ);
            File finalFile = SD.open(filePath, FILE_WRITE);
            
            if (tempFile && finalFile)
            {
                while (tempFile.available())
                {
                    finalFile.write(tempFile.read());
                }
                tempFile.close();
                finalFile.close();
                
                SD.remove(tempPath.c_str()); // Limpiar archivo temporal
                return true;
            }
        }
        else
        {
            Serial.print("Temporary file validation failed: ");
            Serial.println(tempPath);
            SD.remove(tempPath.c_str());
        }
    }
    
    return false;
}

/**
 * @brief Lee un archivo JSON de forma segura con recuperación automática
 * @param filePath Ruta del archivo a leer
 * @param doc Documento JSON donde almacenar el resultado
 * @return true si la lectura fue exitosa, false en caso contrario
 */
bool safeReadJsonFile(const char *filePath, JsonDocument &doc)
{
    File file = SD.open(filePath, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (error)
        {
            Serial.print("Failed to deserialize file: ");
            Serial.println(filePath);
            
            // Intentar recuperar el archivo corrupto
            if (recoverParameterFileFromBackup(filePath))
            {
                // Reintentar la lectura después de la recuperación
                file = SD.open(filePath, FILE_READ);
                if (file)
                {
                    error = deserializeJson(doc, file);
                    file.close();
                    if (!error)
                    {
                        Serial.print("Successfully recovered and read file: ");
                        Serial.println(filePath);
                        return true;
                    }
                    else
                    {
                        Serial.println("Recovery failed, file still corrupted");
                        return false;
                    }
                }
                else
                {
                    Serial.println("Cannot reopen recovered file");
                    return false;
                }
            }
            else
            {
                Serial.println("File recovery failed");
                return false;
            }
        }
        return true;
    }
    else
    {
        Serial.print("Failed to open file: ");
        Serial.println(filePath);
        
        // Intentar recuperar el archivo si no existe
        if (recoverParameterFileFromBackup(filePath))
        {
            // Reintentar la lectura después de la recuperación
            return safeReadJsonFile(filePath, doc);
        }
        return false;
    }
}

#endif // FILERECOVERYFUNCTIONS_HPP
