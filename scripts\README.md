# Gapy Node

This project is a monitoring system based on the ESP32, designed to obtain data from sensors through the RS485 bus and transmit it via LoRa. The system integrates environmental and soil temperature and humidity sensors, soil nutrient sensors, as well as air quality sensors.

The system is configured to transmit data for periods of time and enter a low power mode. The data collected from the sensors is stored in CSV files on an SD card for further analysis.

## Scripts

The `scripts` folder contains supporting scripts for the Gapy Node project. These scripts are used to automate tasks, generate necessary files, and manage versioning and build information.

### generate_buildinfo.py

This script generates build information and stores it in a header file. It is designed to be run during the build process to ensure that the latest build metadata is included in the project.

#### Functionality

- **Determine Latest Release Semver**: Retrieves the latest release tag and parses the semantic versioning components.
- **Determine Current Commit Hash**: Gets the short hash of the current commit.
- **Determine Build Timestamp**: Records the current date and time.
- **Store Results in a Source File**: Writes the retrieved information into `lib/version/buildinfo.hpp`.
- **Export Variables for the Pipeline**: Sets environment variables for use in the CI/CD pipeline.
