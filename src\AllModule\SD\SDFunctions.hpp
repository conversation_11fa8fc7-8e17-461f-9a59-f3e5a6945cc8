// src\AllModule\SD\SDFunctions.hpp
#ifndef SDFUNCTIONS_HPP
#define SDFUNCTIONS_HPP

// Handles errors related to SD card initialization
// Prints an error message to the Serial monitor
void handleSDError()
{
    Serial.println("ERR: SD 01");
    sdNotDetected = true;
}

// Initializes the SD card
// Sets the global flag `sdNotDetected` to true if the SD card cannot be initialized
void initializeSD()
{
    if (!SD.begin(SD_CS_PIN))
    {
        handleSDError();
    }
    else
    {
        Serial.println("STA: SD 01");
    }
}

#endif // src\AllModule\SD\SDFunctions.hpp